# Technical Specification – **Snake Rogue**

**Version:** 0.1 • **Date:** 2025-06-13

## 1. Vision Snapshot
*Snake Rogue* is a fully‑3D, isometric, procedurally expanding roguelike that fuses the timeless “grow‑your‑tail” tension of **Snake** with the high‑energy chaos of bullet‑hell and dungeon‑crawlers. Built in **Unreal Engine 5.4**, it targets buttery‑smooth **120 FPS at 4 K on an RTX 3060** while gracefully scaling down to integrated GPUs.

## 2. Target Platforms
| Tier | OS | GPU (min) | Target FPS |
|------|----|-----------|------------|
| Dev & Release A | Windows 11 (initial), Arch Linux (KDE) | RTX 3060 / RX 6700XT | 120 FPS @ 4 K |
| Release B | SteamDeck / mobile‑class PCs | RDNA2 APU | 60 FPS @ 1080p |
| Stretch | Xbox Series S/X, PS5 | Console GDK | 60 FPS @ 4 K |

## 3. Engine & Toolchain
* **UE 5.4** (Nanite disabled; Lumen on for high tier).
* Build system: **C++ modules + Blueprint wrappers** (90 % gameplay in C++ for determinism).
* **Augment Code** extension for VS Code (see `Augment_Code_Guidelines.md`) provides AI‑assisted refactors, chat, and unit‑test scaffolding.
* Version control: **Git + Git LFS** (locks for binary assets).
* CI: **GitHub Actions** → Windows & Linux runners → automated packaging.

## 4. Game Architecture Overview
```mermaid
graph TD
    CoreLoop -->|ticks| GameplayMode
    GameplayMode --> CameraRig
    GameplayMode --> LevelGen
    GameplayMode --> PowerupSystem
    GameplayMode --> EnemyManager
    LevelGen -->|streams| TileSubsystem
    EnemyManager --> AIController
```

### 4.1 Modules
| Module | Responsibility | Notes |
|--------|----------------|-------|
| **CoreLoop** | High‑level state & roguelike runs | Singleton `UGameState` |
| **CameraRig** | Dynamic tether‑style camera with impulse physics | Uses `UCameraShakeBase` for impact |
| **LevelGen** | Endless expanding arena | Growth vector = opposite quadrant of snake head |
| **PowerupSystem** | Data‑driven buffs & debuffs | `UPowerDataTable` |
| **EnemyManager** | Hazard spawning & AI logic | Behavior Trees |
| **NetSync** | Replication & rollback | Lock‑step for PvP |

## 5. Core Algorithms
* **Procedural Expansion**: Every *N* seconds, pick world‑space direction 180° opposite to current snake centroid → spawn new ring of tiles, push walls outward, recycle far tiles for memory.
* **Snake Movement**: Classic discrete grid step (`Δ=tileSize`) with **easing spline** so movement feels smooth while retaining tile logic.
* **Camera Impulse System**: Rigid‑body spring tether (`F=kx-dv`) + shake curves for collisions, tail‑whips, big kills.

## 6. Data Tables
| Table | Row Count | Key Columns |
|-------|-----------|-------------|
| `DT_Powerups` | 48 | `Id`, `Family`, `EffectCurve`, `TriggerType`, `Duration`, `Magnitude` |
| `DT_Debuffs` | 32 | same as above |
| `DT_Enemies` | 26 | `AIType`, `Health`, `Speed`, `OnDeathAction` |

## 7. Asset Pipeline
* **Blender** for model base mesh → FBX → Unreal.
* **ArmorPaint/Krita** for PBR textures.
* **Particle effects** via Niagara, authored in‑editor.
* **Audio**: Reaper + free SFX libraries, baked into *Sound Cues*.

## 8. Performance Budgets (4 K, 120 FPS)
| Subsystem | ms budget | Notes |
|-----------|-----------|-------|
| GPU base pass | 2.0 | Nanite off |
| Niagara FX | 1.8 | Hard capped emitters |
| AI tick | 0.8 | Budgeted BT & EQS |
| Phys & Collisions | 0.7 | Simplified tails |
| RHI / Misc | 1.2 | |

## 9. Build & Deployment
* **One‑click** GitHub Action builds binaries & publishes to itch.io internal channel.
* Artifact naming: `SnakeRogue_<platform>_<hash>_<date>.zip`.

## 10. External Integrations
* **Augment Code** for AI coding assistance.
* **MCP tools** auto‑invoked by scripts for library lookup & doc caching.

## 11. Glossary
* **Centroid** – geometric center of the entire snake (head + tail).
* **TriggerType** – defines if a buff is *instant*, *player‑triggered*, or *environment‑triggered*.

---
