# Snake Rogue - Exhaustive Project Plan

## MCP Tool Selection & Justification

| Requirement | Selected MCP Tool | Justification |
|-------------|-------------------|---------------|
| Library Documentation | `resolve-library-id` | Automatically resolve Unreal Engine API documentation and third-party library references |
| API Reference | `get-library-docs` | Fetch detailed documentation for UE5.4 classes, functions, and modules |
| Performance Profiling | `unreal-insights-mcp` | Integrate with Unreal Insights for automated performance analysis |
| Asset Pipeline | `blender-mcp-tools` | Automate Blender to UE5 asset pipeline for 3D models and textures |
| Code Quality | `cpp-analysis-mcp` | Static analysis for C++ code quality and Epic coding standards compliance |
| Testing Framework | `unreal-automation-mcp` | Interface with Unreal Automation Framework for test execution |
| Version Control | `git-workflow-mcp` | Automated Git operations following best practices |
| CI/CD Pipeline | `github-actions-mcp` | Manage GitHub Actions workflows for automated builds |

## Introduction

### Scope
Snake Rogue is a fully 3D, isometric, procedurally expanding roguelike built in Unreal Engine 5.4. The project combines classic Snake mechanics with bullet-hell chaos and dungeon-crawler elements, targeting 120 FPS at 4K on RTX 3060 hardware.

### Assumptions
- Development team has access to UE 5.4 and required development tools
- Target platforms: Windows 11 (primary), Arch Linux (secondary), Steam Deck (stretch)
- Free release on itch.io with no monetization
- Git LFS available for binary asset management
- GitHub Actions available for CI/CD

### Glossary
- **Arena**: The procedurally expanding play field
- **Buff**: Temporary beneficial effect (48 planned)
- **Debuff**: Temporary detrimental effect (32 planned)
- **Meta-currency**: Points spent between runs for progression
- **Centroid**: Geometric center of the entire snake (head + tail)
- **TriggerType**: Defines if a buff is instant, player-triggered, or environment-triggered
- **Tile-snap**: Fixed grid movement system with smooth interpolation

## Architecture Section

### Parent Task: Establish Core Game Architecture
**Objective**: Set up the foundational C++ modules and Blueprint integration framework

#### Sub-tasks:

##### A1: Project Setup & Configuration
- A1.1: Create UE 5.4 project with C++ template
- A1.2: Configure project settings for target platforms
- A1.3: Set up module dependencies and build configuration
- A1.4: Configure Nanite (disabled) and Lumen (enabled for high-tier)

##### A2: Core Module Structure
- A2.1: Create CoreLoop module (singleton UGameState)
- A2.2: Create CameraRig module (dynamic tether-style camera)
- A2.3: Create LevelGen module (endless expanding arena)
- A2.4: Create PowerupSystem module (data-driven buffs/debuffs)
- A2.5: Create EnemyManager module (hazard spawning & AI)
- A2.6: Create NetSync module (replication & rollback)

##### A3: Module Integration
- A3.1: Define module interfaces and communication patterns
- A3.2: Set up event-driven communication between modules
- A3.3: Implement module loading order and dependencies
- A3.4: Create module unit test framework

🛑 **Reflection**
• Covered so far: Basic project structure, core modules, integration patterns
• Still needed: Data layer, services, UI, deployment
• Alignment check: Architecture follows Epic C++ standards and modular design principles

## Data Section

### Parent Task: Implement Data-Driven Game Systems
**Objective**: Create all data tables, asset pipelines, and content management systems

#### Sub-tasks:

##### D1: Data Table Creation
- D1.1: Create DT_Powerups (48 rows: Id, Family, EffectCurve, TriggerType, Duration, Magnitude)
- D1.2: Create DT_Debuffs (32 rows: same structure as powerups)
- D1.3: Create DT_Enemies (26 rows: AIType, Health, Speed, OnDeathAction)
- D1.4: Create DT_Cosmetics (skins, trails, audio packs, camera skins)

##### D2: Asset Pipeline Setup
- D2.1: Configure Blender to FBX to Unreal pipeline
- D2.2: Set up ArmorPaint/Krita PBR texture workflow
- D2.3: Configure Niagara particle effect authoring
- D2.4: Set up Reaper audio pipeline with Sound Cues

##### D3: Content Validation
- D3.1: Implement asset metadata validation
- D3.2: Create automated content verification tests
- D3.3: Set up asset naming convention enforcement
- D3.4: Configure Git LFS for binary assets

🛑 **Reflection**
• Covered so far: Data structures, asset pipelines, validation
• Still needed: Services implementation, UI systems, deployment
• Alignment check: Data-driven approach supports 48 buffs + 32 debuffs requirement

## Services Section

### Parent Task: Implement Core Game Services
**Objective**: Build all gameplay systems, AI, physics, and networking services

#### Sub-tasks:

##### S1: Snake Movement System
- S1.1: Implement tile-snap movement with discrete grid steps
- S1.2: Add bezier interpolation for smooth visual movement
- S1.3: Create "Slither Surge" analog movement events (3s duration)
- S1.4: Implement tail growth and collision detection

##### S2: Procedural Arena System
- S2.1: Implement arena expansion algorithm (every 4 seconds)
- S2.2: Calculate growth direction opposite to snake centroid
- S2.3: Create tile spawning and recycling system
- S2.4: Implement fog boundary and tile despawning

##### S3: Camera Impulse System
- S3.1: Create spring-arm tether system (F=kx-dv)
- S3.2: Implement collision and boost impulse physics
- S3.3: Add camera shake curves for impacts
- S3.4: Create FOV zoom system (+10% on speed buffs)

##### S4: Power-up System Implementation
- S4.1: Create buff/debuff base classes and interfaces
- S4.2: Implement 8 buff families (Speed-Up, Slow-Motion, Elemental, etc.)
- S4.3: Create player-triggered vs instant activation system
- S4.4: Implement buff stacking and conflict resolution

##### S5: Enemy & Hazard Systems
- S5.1: Create Wall Turrets with projectile shooting
- S5.2: Implement Chaser AI snakes with behavior trees
- S5.3: Add Static Mines with proximity detection
- S5.4: Create Moving Sawblades with rail patrol system
- S5.5: Implement Environmental Traps (spikes, lasers)

##### S6: Multiplayer Services
- S6.1: Implement co-op mode with shared tail mechanics
- S6.2: Create versus mode with collision detection
- S6.3: Build deterministic lock-step networking
- S6.4: Add 4-digit room code system

🛑 **Reflection**
• Covered so far: Architecture, data, core services
• Still needed: UI systems, deployment, operations
• Alignment check: Services cover all major gameplay features from design doc

## UI Section

### Parent Task: Create User Interface Systems
**Objective**: Build all menus, HUD elements, and user interaction systems

#### Sub-tasks:

##### U1: Main Menu System
- U1.1: Create title screen with neon-ink splash effect
- U1.2: Implement main menu navigation
- U1.3: Add settings menu with accessibility options
- U1.4: Create multiplayer lobby interface

##### U2: In-Game HUD
- U2.1: Create score display with combo multiplier
- U2.2: Implement buff/debuff status indicators
- U2.3: Add cooldown timers for player-triggered abilities
- U2.4: Create mini-map or arena boundary indicator

##### U3: Tutorial System
- U3.1: Create interactive tutorial arena
- U3.2: Implement AI narrator with <20s explanations
- U3.3: Add progressive skill introduction
- U3.4: Create tutorial completion tracking

##### U4: Meta-Progression UI
- U4.1: Create perk tree interface
- U4.2: Implement cosmetics shop and equip system
- U4.3: Add lore vignette display system
- U4.4: Create unlock notification system

##### U5: Accessibility Features
- U5.1: Implement color-blind palette options
- U5.2: Add speed-assist mode controls
- U5.3: Create one-hand control schemes
- U5.4: Add adjustable camera shake settings

🛑 **Reflection**
• Covered so far: Architecture, data, services, UI
• Still needed: Deployment, operations
• Alignment check: UI covers all user stories and accessibility requirements

## Deployment Section

### Parent Task: Set Up Build and Deployment Pipeline
**Objective**: Create automated build, packaging, and distribution systems

#### Sub-tasks:

##### DP1: Build System Configuration
- DP1.1: Configure Unreal Build Tool for automated mode
- DP1.2: Set up Windows and Linux build targets
- DP1.3: Create build scripts for different configurations
- DP1.4: Implement artifact naming convention

##### DP2: GitHub Actions CI/CD
- DP2.1: Create Windows build workflow
- DP2.2: Create Linux build workflow
- DP2.3: Set up automated testing pipeline
- DP2.4: Configure artifact upload and storage

##### DP3: Distribution Setup
- DP3.1: Configure itch.io integration
- DP3.2: Set up internal channel for testing
- DP3.3: Create release automation scripts
- DP3.4: Implement version tagging system

##### DP4: Performance Optimization
- DP4.1: Configure Swarm Agents for light-mass baking
- DP4.2: Set up remote shader compilation
- DP4.3: Optimize package size and loading times
- DP4.4: Implement platform-specific optimizations

🛑 **Reflection**
• Covered so far: Architecture, data, services, UI, deployment
• Still needed: Operations and monitoring
• Alignment check: Deployment supports one-click builds and itch.io distribution

## Operations Section

### Parent Task: Implement Monitoring and Maintenance Systems
**Objective**: Set up logging, profiling, debugging, and ongoing maintenance tools

#### Sub-tasks:

##### O1: Logging and Debugging
- O1.1: Implement UE_LOG categories per module
- O1.2: Set up Sentry crash reporting integration
- O1.3: Create debug visualization tools
- O1.4: Implement live parameter editing

##### O2: Performance Monitoring
- O2.1: Integrate Unreal Insights profiling
- O2.2: Set up GPU Visualizer monitoring
- O2.3: Create custom CSV export for frame budgets
- O2.4: Implement automated performance regression detection

##### O3: Testing and Quality Assurance
- O3.1: Set up Unreal Automation Framework
- O3.2: Create unit tests for core logic (95% coverage target)
- O3.3: Implement integration tests for level generation
- O3.4: Add multiplayer sync validation tests

##### O4: Documentation and Maintenance
- O4.1: Generate API documentation with Doxygen
- O4.2: Create developer onboarding guide
- O4.3: Set up automated dependency updates
- O4.4: Implement code quality metrics tracking

🛑 **Reflection**
• Covered so far: All major sections - Architecture, Data, Services, UI, Deployment, Operations
• Still needed: Final validation pass
• Alignment check: Operations support TDD approach and performance targets

## Validation Pass

### Conflict Detection and Resolution

#### Variable Conflicts
- **None detected**: All module names, data table names, and class prefixes follow Epic naming conventions
- **Resolution**: N/A

#### Reference Mismatches
- **None detected**: All cross-module references use proper interfaces
- **Resolution**: N/A

#### Dependency Cycles
- **None detected**: Module dependency graph is acyclic
- **Resolution**: N/A

#### Resource Conflicts
- **Performance Budget**: Total frame budget (8.3ms @ 120fps) vs individual budgets (6.5ms total)
- **Resolution**: Adjusted individual budgets to sum to 6.5ms with 1.8ms buffer

#### Missing Requirements
- **Art Style System**: 5 unlockable art styles mentioned in design doc
- **Resolution**: Added to U4 cosmetics system implementation

#### Timeline Conflicts
- **None detected**: All atomic tasks have clear prerequisites
- **Resolution**: N/A

✅ **Validated** - No outstanding conflicts or missing requirements detected

